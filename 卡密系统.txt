// ==UserScript==
// @name         橙光无限花
// @version      *******
// @namespace    Janicerser
// @description  卡密验证版本
// <AUTHOR>
// @icon         https://m.riyugo.cn/i/2025/07/17/s934a4.jpg
// @grant        none
// @match        https://*.66rpg.com/h5/*
 
// ==/UserScript==
 
(function () {
 
  'use strict';
 
  // 配置信息
  const CONFIG = {
    API_URL: 'https://cg.c01.me/api.php',
    APP_ID: '10002', 
    STORAGE_KEY: 'orangelight_auth_data'
  };
 
  // MD5加密函数
  function kmMD5(string) {
    function RotateLeft(lValue, iShiftBits) {
      return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }
 
    function AddUnsigned(lX, lY) {
      var lX4, lY4, lX8, lY8, lResult;
      lX8 = (lX & 0x80000000);
      lY8 = (lY & 0x80000000);
      lX4 = (lX & 0x40000000);
      lY4 = (lY & 0x40000000);
      lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
      if (lX4 & lY4) {
        return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
      }
      if (lX4 | lY4) {
        if (lResult & 0x40000000) {
          return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
        } else {
          return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
        }
      } else {
        return (lResult ^ lX8 ^ lY8);
      }
    }
 
    function F(x, y, z) { return (x & y) | ((~x) & z); }
    function G(x, y, z) { return (x & z) | (y & (~z)); }
    function H(x, y, z) { return (x ^ y ^ z); }
    function I(x, y, z) { return (y ^ (x | (~z))); }
 
    function FF(a, b, c, d, x, s, ac) {
      a = AddUnsigned(a, AddUnsigned(AddUnsigned(F(b, c, d), x), ac));
      return AddUnsigned(RotateLeft(a, s), b);
    }
 
    function GG(a, b, c, d, x, s, ac) {
      a = AddUnsigned(a, AddUnsigned(AddUnsigned(G(b, c, d), x), ac));
      return AddUnsigned(RotateLeft(a, s), b);
    }
 
    function HH(a, b, c, d, x, s, ac) {
      a = AddUnsigned(a, AddUnsigned(AddUnsigned(H(b, c, d), x), ac));
      return AddUnsigned(RotateLeft(a, s), b);
    }
 
    function II(a, b, c, d, x, s, ac) {
      a = AddUnsigned(a, AddUnsigned(AddUnsigned(I(b, c, d), x), ac));
      return AddUnsigned(RotateLeft(a, s), b);
    }
 
    function ConvertToWordArray(string) {
      var lWordCount;
      var lMessageLength = string.length;
      var lNumberOfWords_temp1 = lMessageLength + 8;
      var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
      var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
      var lWordArray = Array(lNumberOfWords - 1);
      var lBytePosition = 0;
      var lByteCount = 0;
      while (lByteCount < lMessageLength) {
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition));
        lByteCount++;
      }
      lWordCount = (lByteCount - (lByteCount % 4)) / 4;
      lBytePosition = (lByteCount % 4) * 8;
      lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
      lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
      lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
      return lWordArray;
    }
 
    function WordToHex(lValue) {
      var WordToHexValue = "", WordToHexValue_temp = "", lByte, lCount;
      for (lCount = 0; lCount <= 3; lCount++) {
        lByte = (lValue >>> (lCount * 8)) & 255;
        WordToHexValue_temp = "0" + lByte.toString(16);
        WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
      }
      return WordToHexValue;
    }
 
    function Utf8Encode(string) {
      string = string.replace(/\r\n/g, "\n");
      var utftext = "";
 
      for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
 
        if (c < 128) {
          utftext += String.fromCharCode(c);
        }
        else if ((c > 127) && (c < 2048)) {
          utftext += String.fromCharCode((c >> 6) | 192);
          utftext += String.fromCharCode((c & 63) | 128);
        }
        else {
          utftext += String.fromCharCode((c >> 12) | 224);
          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
          utftext += String.fromCharCode((c & 63) | 128);
        }
      }
 
      return utftext;
    }
 
    var x = Array();
    var k, AA, BB, CC, DD, a, b, c, d;
    var S11 = 7, S12 = 12, S13 = 17, S14 = 22;
    var S21 = 5, S22 = 9, S23 = 14, S24 = 20;
    var S31 = 4, S32 = 11, S33 = 16, S34 = 23;
    var S41 = 6, S42 = 10, S43 = 15, S44 = 21;
 
    string = Utf8Encode(string);
    x = ConvertToWordArray(string);
    a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
 
    for (k = 0; k < x.length; k += 16) {
      AA = a; BB = b; CC = c; DD = d;
      a = FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
      d = FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
      c = FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
      b = FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
      a = FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
      d = FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
      c = FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
      b = FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
      a = FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
      d = FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
      c = FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
      b = FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
      a = FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
      d = FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
      c = FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
      b = FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
      a = GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
      d = GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
      c = GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
      b = GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
      a = GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
      d = GG(d, a, b, c, x[k + 10], S22, 0x2441453);
      c = GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
      b = GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
      a = GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
      d = GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
      c = GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
      b = GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
      a = GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
      d = GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
      c = GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
      b = GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
      a = HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
      d = HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
      c = HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
      b = HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
      a = HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
      d = HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
      c = HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
      b = HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
      a = HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
      d = HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
      c = HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
      b = HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
      a = HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
      d = HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
      c = HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
      b = HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
      a = II(a, b, c, d, x[k + 0], S41, 0xF4292244);
      d = II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
      c = II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
      b = II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
      a = II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
      d = II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
      c = II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
      b = II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
      a = II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
      d = II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
      c = II(c, d, a, b, x[k + 6], S43, 0xA3014314);
      b = II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
      a = II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
      d = II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
      c = II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
      b = II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
      a = AddUnsigned(a, AA);
      b = AddUnsigned(b, BB);
      c = AddUnsigned(c, CC);
      d = AddUnsigned(d, DD);
    }
 
    var temp = WordToHex(a) + WordToHex(b) + WordToHex(c) + WordToHex(d);
    return temp.toLowerCase();
  }
 
  // 生成设备码
  function generateDeviceCode() {
    try {
      // 尝试获取已保存的设备ID
      var deviceId = localStorage.getItem('kmDeviceId');
 
      // 如果没有保存过设备ID，则生成一个新的
      if (!deviceId) {
        // 生成一个基于设备信息的唯一ID
        var navigator_info = window.navigator;
        var screen_info = window.screen;
        var uid = navigator_info.mimeTypes.length;
        uid += navigator_info.userAgent.replace(/\D+/g, '');
        uid += navigator_info.plugins.length;
        uid += screen_info.height || '';
        uid += screen_info.width || '';
        uid += screen_info.pixelDepth || '';
 
        // 生成MD5哈希
        deviceId = kmMD5(uid + new Date().getTime());
 
        // 保存设备ID
        localStorage.setItem('kmDeviceId', deviceId);
      }
 
      return deviceId;
    } catch (e) {
      console.error("生成设备ID失败:", e);
      // 如果出错，返回一个随机ID
      return 'device_' + Math.random().toString(36).substr(2, 9);
    }
  }
 
  // 代理服务配置
  const proxyServices = [
    {
      name: "CORS.EU",
      url: "https://cors.eu.org/",
      type: "url",
      parseResponse: function(data) {
        return data; // 直接返回原始数据
      }
    },
    {
      name: "小蜜蜂",
      url: "https://cors.zme.ink/",
      type: "url",
      parseResponse: function(data) {
        return data; // 直接返回原始数据
      }
    },
    {
      name: "iSteed",
      url: "https://cors.isteed.cc/",
      type: "url",
      parseResponse: function(data) {
        return data; // 直接返回原始数据
      }
    },
    {
      name: "CORS Proxy",
      url: "https://corsproxy.io/?",
      type: "url",
      parseResponse: function(data) {
        return data;
      }
    }
  ];
 
  // 当前使用的代理索引
  let currentProxyIndex = 0;
 
  // 获取下一个代理
  function getNextProxy() {
    currentProxyIndex = (currentProxyIndex + 1) % proxyServices.length;
    return proxyServices[currentProxyIndex];
  }
 
  // 使用jQuery的AJAX请求（如果没有jQuery则使用fetch）
  function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      if (typeof $ !== 'undefined' && $.ajax) {
        // 使用jQuery
        $.ajax({
          url: url,
          type: options.method || "GET",
          dataType: "json",
          timeout: options.timeout || 5000,
          success: function(data) {
            resolve(data);
          },
          error: function(xhr, status, error) {
            reject(new Error(`${status}: ${error}`));
          }
        });
      } else {
        // 使用fetch
        fetch(url, {
          method: options.method || 'GET',
          signal: AbortSignal.timeout(options.timeout || 5000)
        })
        .then(response => response.json())
        .then(data => resolve(data))
        .catch(error => reject(error));
      }
    });
  }
 
  // 验证卡密
  async function verifyKami(kami, isAuto = false) {
    const deviceCode = generateDeviceCode();
 
    // 构建API请求URL
    const apiUrl = `${CONFIG.API_URL}?api=kmlogon&app=${CONFIG.APP_ID}&kami=${encodeURIComponent(kami)}&markcode=${encodeURIComponent(deviceCode)}`;
 
    // 获取当前代理
    const proxy = proxyServices[currentProxyIndex];
    console.log("使用代理服务:", proxy.name);
 
    // 记录请求开始时间
    const requestStartTime = new Date().getTime();
 
    try {
      let response;
 
      if (proxy.type === "url") {
        // URL参数类型的代理
        const proxyUrl = proxy.url + encodeURIComponent(apiUrl);
        response = await makeRequest(proxyUrl, { timeout: 5000 });
 
        // 记录响应时间
        const requestTime = new Date().getTime() - requestStartTime;
        console.log("代理响应时间:", requestTime + "ms", "代理:", proxy.name);
        proxy.lastResponseTime = requestTime;
 
        // 解析响应
        response = proxy.parseResponse(response);
      } else if (proxy.type === "direct") {
        // 直接请求
        response = await makeRequest(apiUrl, { timeout: 5000 });
      }
 
      return handleVerifyResponse(response, kami, isAuto);
 
    } catch (error) {
      console.error("验证请求失败:", error, "代理:", proxy.name);
 
      // 尝试下一个代理
      if (!window.verifyRetryCount) {
        window.verifyRetryCount = 1;
      } else {
        window.verifyRetryCount++;
      }
 
      // 如果尝试了所有代理仍然失败
      if (window.verifyRetryCount >= proxyServices.length) {
        window.verifyRetryCount = 0;
        return { success: false, message: '所有代理都无法连接，请检查网络' };
      }
 
      // 切换到下一个代理重试
      getNextProxy();
      console.log("切换到下一个代理重试:", proxyServices[currentProxyIndex].name);
 
      // 延迟500ms后重试
      await new Promise(resolve => setTimeout(resolve, 500));
      return verifyKami(kami, isAuto);
    }
  }
 
  // 处理验证响应
  function handleVerifyResponse(response, kami, isAuto) {
    console.log("验证响应:", response);
 
    if (response && response.code == 200) {
      // 验证成功，保存验证信息
      const authData = {
        kami: kami,
        vip: response.msg.vip,
        deviceCode: generateDeviceCode(),
        verifyTime: Date.now()
      };
      localStorage.setItem(CONFIG.STORAGE_KEY, JSON.stringify(authData));
 
      // 重置重试计数器
      window.verifyRetryCount = 0;
 
      return { success: true, data: authData };
    } else {
      // 验证失败
      const message = response && response.msg ? response.msg : "卡密无效";
 
      // 如果是自动验证失败，清除保存的卡密
      if (isAuto) {
        localStorage.removeItem('kmSavedKami');
        console.log('自动验证失败，已清除保存的卡密');
      }
 
      return { success: false, message: message };
    }
  }
 
  // 辅助函数：补零
  function padZero(num) {
    return num < 10 ? '0' + num : num;
  }
 
  // 检查本地验证状态
  function checkLocalAuth() {
    const authData = localStorage.getItem(CONFIG.STORAGE_KEY);
    if (!authData) return null;
 
    try {
      const data = JSON.parse(authData);
      const currentTime = Math.floor(Date.now() / 1000);
 
      // 检查是否过期
      if (data.vip && parseInt(data.vip) > currentTime) {
        return data;
      } else {
        // 已过期，清除本地数据
        localStorage.removeItem(CONFIG.STORAGE_KEY);
        return null;
      }
    } catch (error) {
      localStorage.removeItem(CONFIG.STORAGE_KEY);
      return null;
    }
  }
 
  // 检查保存的卡密
  function checkSavedKami() {
    try {
      const savedKami = localStorage.getItem('kmSavedKami');
      return savedKami;
    } catch (e) {
      console.error("读取保存的卡密失败:", e);
      return null;
    }
  }
 
  // 格式化到期时间
  function formatExpireTime(timestamp) {
    if (!timestamp) return "授权已激活";
 
    const timestampNum = parseInt(timestamp);
    if (isNaN(timestampNum)) return "授权已激活";
 
    const endTime = new Date(timestampNum * 1000);
    return "到期时间: " + endTime.getFullYear() + "-" +
           padZero(endTime.getMonth() + 1) + "-" +
           padZero(endTime.getDate()) + " " +
           padZero(endTime.getHours()) + ":" +
           padZero(endTime.getMinutes());
  }
 
  // 创建验证弹窗
  function createAuthModal() {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 99999;
      display: flex;
      justify-content: center;
      align-items: center;
    `;
 
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
      background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
      padding: 25px;
      border-radius: 15px;
      border: 3px solid #DB7093;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      text-align: center;
      min-width: 300px;
    `;
 
    modalContent.innerHTML = `
      <h2 style="color: #8B008B; margin-bottom: 20px; font-size: 24px;">卡密验证</h2>
      <div style="margin-bottom: 15px;">
        <input type="text" id="kamiInput" placeholder="请输入卡密" style="
          width: 250px;
          padding: 10px;
          border: 2px solid #DB7093;
          border-radius: 8px;
          font-size: 14px;
          text-align: center;
        ">
      </div>
      <div style="margin-bottom: 15px;">
        <label style="color: #8B008B; font-size: 12px; display: flex; align-items: center; justify-content: center;">
          <input type="checkbox" id="rememberKami" style="margin-right: 6px;">
          记住卡密（下次自动验证）
        </label>
      </div>
      <div style="margin-bottom: 15px;">
        <button id="verifyBtn" style="
          background: #DB7093;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          font-size: 14px;
          cursor: pointer;
          margin-right: 8px;
        ">验证</button>
        <button id="cancelBtn" style="
          background: #999;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          font-size: 14px;
          cursor: pointer;
        ">取消</button>
      </div>
      <div id="statusMsg" style="color: #8B008B; font-size: 14px; min-height: 20px;"></div>
    `;
 
    modal.appendChild(modalContent);
    document.body.appendChild(modal);
 
    return {
      modal,
      kamiInput: modal.querySelector('#kamiInput'),
      verifyBtn: modal.querySelector('#verifyBtn'),
      cancelBtn: modal.querySelector('#cancelBtn'),
      statusMsg: modal.querySelector('#statusMsg')
    };
  }
 
  // 显示成功信息
  function showSuccessInfo(authData) {
    const expireTime = formatExpireTime(authData.vip);
    const successModal = document.createElement('div');
    successModal.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
      padding: 25px;
      border-radius: 15px;
      border: 3px solid #DB7093;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      z-index: 99999;
      text-align: center;
      min-width: 300px;
    `;
 
    successModal.innerHTML = `
      <h3 style="color: #8B008B; margin-bottom: 15px;">✅ 验证成功！</h3>
      <p style="color: #8B008B; margin-bottom: 10px;">卡密: ${authData.kami}</p>
      <p style="color: #8B008B; margin-bottom: 15px;">${expireTime}</p>
      <button onclick="this.parentElement.remove()" style="
        background: #DB7093;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
      ">确定</button>
    `;
 
    document.body.appendChild(successModal);
 
    // 3秒后自动关闭
    setTimeout(() => {
      if (successModal.parentElement) {
        successModal.remove();
      }
    }, 3000);
  }
 
  // 在线验证卡密状态
  async function verifyOnlineStatus() {
    const authData = checkLocalAuth();
    if (!authData) {
      console.log('没有本地验证数据，需要重新验证');
      return false;
    }
 
    console.log('检查在线卡密状态...');
    try {
      const result = await verifyKami(authData.kami, true);
      if (result.success) {
        console.log('在线验证通过');
        return true;
      } else {
        console.log('在线验证失败，卡密可能已被禁用:', result.message);
        // 清除本地数据
        localStorage.removeItem(CONFIG.STORAGE_KEY);
        localStorage.removeItem('kmSavedKami');
 
        // 显示错误信息并重新验证
        alert('卡密已失效或被禁用，请重新验证！\n错误信息: ' + result.message);
        location.reload();
        return false;
      }
    } catch (error) {
      console.error('在线验证出错:', error);
      return true; // 网络错误时不强制重新验证
    }
  }
 
  // 启动定时检查
  function startPeriodicCheck() {
    // 每30分钟检查一次
    setInterval(async () => {
      console.log('执行定时卡密状态检查...');
      await verifyOnlineStatus();
    }, 30 * 60 * 1000); // 30分钟 = 30 * 60 * 1000 毫秒
  }
 
  // 页面可见性检查
  function setupVisibilityCheck() {
    let lastCheckTime = Date.now();
 
    document.addEventListener('visibilitychange', async () => {
      if (!document.hidden) {
        const now = Date.now();
        // 如果页面隐藏超过5分钟，重新检查验证状态
        if (now - lastCheckTime > 5 * 60 * 1000) {
          console.log('页面重新激活，检查卡密状态...');
          await verifyOnlineStatus();
        }
        lastCheckTime = now;
      }
    });
 
    // 监听窗口焦点事件
    window.addEventListener('focus', async () => {
      const now = Date.now();
      if (now - lastCheckTime > 5 * 60 * 1000) {
        console.log('窗口重新获得焦点，检查卡密状态...');
        await verifyOnlineStatus();
        lastCheckTime = now;
      }
    });
  }
 
  // 初始化验证流程
  function initAuth() {
    // 先检查本地验证状态
    const localAuth = checkLocalAuth();
    if (localAuth) {
      console.log('本地验证有效，进行在线状态检查');
      // 进行在线验证检查
      verifyOnlineStatus().then(isValid => {
        if (isValid) {
          showSuccessInfo(localAuth);
          initMainFeatures();
          // 启动定时检查和页面可见性检查
          startPeriodicCheck();
          setupVisibilityCheck();
        }
        // 如果无效，verifyOnlineStatus 内部已经处理了重新验证
      });
      return;
    }
 
    // 检查是否有保存的卡密
    const savedKami = checkSavedKami();
    if (savedKami) {
      console.log('发现保存的卡密，尝试自动验证');
      // 显示验证中状态
      showVerifyingStatus();
      // 自动验证
      verifyKami(savedKami, true).then(result => {
        hideVerifyingStatus();
        if (result.success) {
          showSuccessInfo(result.data);
          initMainFeatures();
          // 启动定时检查和页面可见性检查
          startPeriodicCheck();
          setupVisibilityCheck();
        } else {
          // 自动验证失败，显示手动验证界面
          console.log('自动验证失败:', result.message);
          showManualVerifyModal();
        }
      });
      return;
    }
 
    // 需要手动验证
    showManualVerifyModal();
  }
 
  // 显示验证中状态
  function showVerifyingStatus() {
    const statusDiv = document.createElement('div');
    statusDiv.id = 'verifyingStatus';
    statusDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #FFB6C1, #FFC0CB);
      padding: 20px;
      border-radius: 10px;
      border: 2px solid #DB7093;
      z-index: 99999;
      text-align: center;
      color: #8B008B;
      font-size: 16px;
    `;
    statusDiv.textContent = '正在自动验证卡密...';
    document.body.appendChild(statusDiv);
  }
 
  // 隐藏验证中状态
  function hideVerifyingStatus() {
    const statusDiv = document.getElementById('verifyingStatus');
    if (statusDiv) {
      statusDiv.remove();
    }
  }
 
  // 显示手动验证弹窗
  function showManualVerifyModal() {
    const authModal = createAuthModal();
 
    authModal.verifyBtn.onclick = async () => {
      const kami = authModal.kamiInput.value.trim();
      if (!kami) {
        authModal.statusMsg.textContent = '请输入卡密';
        authModal.statusMsg.style.color = 'red';
        return;
      }
 
      authModal.verifyBtn.disabled = true;
      authModal.verifyBtn.textContent = '验证中...';
      authModal.statusMsg.textContent = '正在验证，请稍候...';
      authModal.statusMsg.style.color = '#8B008B';
 
      const result = await verifyKami(kami, false);
 
      if (result.success) {
        // 如果勾选了记住卡密，则保存
        const rememberCheckbox = authModal.modal.querySelector('#rememberKami');
        if (rememberCheckbox && rememberCheckbox.checked) {
          localStorage.setItem('kmSavedKami', kami);
          console.log('已保存卡密');
        }
 
        authModal.statusMsg.textContent = '验证成功！';
        authModal.statusMsg.style.color = 'green';
 
        setTimeout(() => {
          authModal.modal.remove();
          showSuccessInfo(result.data);
          initMainFeatures();
          // 启动定时检查和页面可见性检查
          startPeriodicCheck();
          setupVisibilityCheck();
        }, 1000);
      } else {
        authModal.statusMsg.textContent = result.message;
        authModal.statusMsg.style.color = 'red';
        authModal.verifyBtn.disabled = false;
        authModal.verifyBtn.textContent = '验证';
      }
    };
 
    authModal.cancelBtn.onclick = () => {
      authModal.modal.remove();
    };
 
    authModal.kamiInput.onkeypress = (e) => {
      if (e.key === 'Enter') {
        authModal.verifyBtn.click();
      }
    };
  }
 
  // 初始化主要功能
  function initMainFeatures() {
 
    const m = document.createElement("button");
    m.textContent = "janiceser";
    m.style.backgroundColor = "rgba(128, 128, 128, 0.5)";
    m.style.background = "#FFB6C1";
    m.style.color = "white";
    m.style.border = "2px solid #DB7093";
    m.style.zIndex = "9999";
    m.style.borderRadius = "30px";
    m.style.padding = "8px 10px";
    m.style.position = "fixed";
    m.style.top = "20px";
    m.style.left = "20px";
    m.style.select = "none";
    m.style.cursor = "pointer";
    document.body.appendChild(m);
 
    const n = document.createElement("div");
    n.style.position = "fixed";
    n.style.top = "55px";
    n.style.left = "20px";
    n.style.backgroundColor = "rgba(255, 255, 255, 0)";
    n.style.border = "#FFB6C1";
    n.style.display = "none";
    n.style.zIndex = "9998";
    document.body.appendChild(n);
 
    const a = (b, a) => {
      const c = document.createElement("div");
      c.textContent = b;
      c.style.padding = "11px 16px";
      c.style.cursor = "pointer";
      c.style.border = "2px solid #DB7093";
      c.style.margin = "3px";
      c.style.backgroundColor = "rgba(128, 128, 128, 0.5)";
      c.style.background = "#FFB6C1";
      c.style.color = "white";
      c.style.borderRadius = "20px";
      c.style.select = "none";
      c.onclick = a;
      n.appendChild(c);
      return c;
    };
 
    m.onclick = () => {
      n.style.display = n.style.display === "none" ? "block" : "none";
    };
 
    // 通用全屏切换函数
    const toggleFullScreen = () => {
      // 检查是否已全屏
      const isFullscreen = !!document.fullscreenElement ||
                           !!document.webkitFullscreenElement ||
                           !!document.mozFullScreenElement ||
                           !!document.msFullscreenElement;
 
      if (!isFullscreen) {
        // 进入全屏
        const docEl = document.documentElement;
        if (docEl.requestFullscreen) {
          docEl.requestFullscreen().catch(err => {
            console.error(`进入全屏失败: ${err.message}`);
          });
        } else if (docEl.webkitRequestFullscreen) { // Chrome, Safari
          docEl.webkitRequestFullscreen();
        } else if (docEl.mozRequestFullScreen) { // Firefox
          docEl.mozRequestFullScreen();
        } else if (docEl.msRequestFullscreen) { // IE/Edge
          docEl.msRequestFullscreen();
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    };
 
    // 显示验证状态
    const authData = checkLocalAuth();
    if (authData) {
      const expireTime = formatExpireTime(authData.vip);
      a(`验证状态: 已激活`, () => {
        alert(`卡密: ${authData.kami}\n到期时间: ${expireTime}`);
      });
    }
 
    a("商城开关", function () {
      q = !q;
      this.innerText = q ? "商城开启" : "商城关闭";
      if (q) {
        console.log("拦截器已开启");
      } else {
        console.log("拦截器已关闭");
      }
    });
 
    // 添加全屏按钮
    a("切换全屏", toggleFullScreen);
 
    a("修改累充", () => {
      const inputAmount = prompt("请填写需要的鲜花数量大小");
      if (inputAmount) {
        var userData = getUserData();
        ['totalFlower', 'freshFlower', 'wildFlower', 'tempFlower', 'realFlower', 'haveFlower'].forEach(function(flowerType) {
          userData[flowerType] = inputAmount;
        });
        // 累充 = 鲜花数量("填写鲜花数值")
      }
    });
 
 
 
    a("退出菜单", () => {
      n.style.display = "none";
    });
 
    a("屏蔽按钮", () => {
      m.style.display = "none";
    });
  function o() {
    const f = new Date();
    const a = f.getFullYear().toString();
    const b = p(f.getMonth() + 1);
    const c = p(f.getDate());
    const d = p(f.getHours());
    const e = p(f.getMinutes());
    const g = p(f.getSeconds());
    const h = p(f.getMilliseconds(), 4);
    return "" + a + b + c + d + e + g + h;
  }
  function p(d, a = 2) {
    let b = d.toString();
    while (b.length < a) {
      b = "0" + b;
    }
    return b;
  }
  let q = false;
  const c = [];
  c.push({
    match: b => b.includes("/createBuyOrder"),
    modify: (e, a) => {
      const b = new URLSearchParams(a.split("?")[1]);
      const c = b.get("goods_id");
      const d = b.get("buy_num");
      const f = o();
      const g = {
        goods_id: c,
        order_id: "${orderId}",
        buy_num: d
      };
      const h = {
        status: 1,
        msg: "successful",
        data: g
      };
      return JSON.stringify(h);
    }
  });
 
    // \u62e6\u622aXMLHttpRequest\u8bf7\u6c42
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        // \u5904\u7406URL\uff0c\u5220\u9664sign\u53c2\u6570
        const processedUrl = removeSignParameter(url);
        console.log(`[XHR] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);
 
        return originalXhrOpen.call(this, method, processedUrl, async, user, password);
    };
 
    // \u62e6\u622aFetch API\u8bf7\u6c42
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        let url = typeof input === 'string' ? input : input.url;
        const processedUrl = removeSignParameter(url);
        console.log(`[Fetch] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);
 
        if (typeof input !== 'string') {
            input = { ...input, url: processedUrl };
        }
 
        return originalFetch.call(this, processedUrl, init);
    };
 
    // \u62e6\u622aJSONP\u8bf7\u6c42\uff08\u52a8\u6001\u521b\u5efa\u7684script\u6807\u7b7e\uff09
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(document, tagName);
 
        if (tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;
 
            element.setAttribute = function(name, value) {
                if (name === 'src') {
                    const processedUrl = removeSignParameter(value);
                    console.log(`[Script] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);
                    return originalSetAttribute.call(this, name, processedUrl);
                }
                return originalSetAttribute.call(this, name, value);
            };
        }
 
        return element;
    };
 
    // \u5904\u7406URL\uff0c\u5220\u9664sign\u53c2\u6570
    function removeSignParameter(url) {
        try {
            const urlObj = new URL(url);
 
            // \u68c0\u67e5\u5e76\u5220\u9664sign\u53c2\u6570
            if (urlObj.searchParams.has('sign')) {
                urlObj.searchParams.delete('sign');
                console.log(`\u5df2\u5220\u9664URL\u4e2d\u7684sign\u53c2\u6570: ${urlObj.pathname}`);
                return urlObj.toString();
            }
        } catch (error) {
            console.error(`\u5904\u7406URL\u5931\u8d25: ${url}`, error);
        }
 
        return url;
    }
 
    console.log('Remove Sign Parameter 脚本已加载');
 
  const h = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function (g, i, a = true, b = null, d = null) {
    this._url = i;
    if (!q) {
      return h.call(this, g, i, a, b, d);
    }
    h.apply(this, arguments);
    this.addEventListener("readystatechange", () => {
      if (this.readyState === 4 && this.status === 200) {
        let d = this.responseText;
        c.forEach(a => {
          if (a.match(this._url)) {
            try {
              d = a.modify(d, this._url);
              console.log("\u62e6\u622a\u6210\u529f: " + this._url);
            } catch (b) {
              console.error("\u62e6\u622a\u5931\u8d25: " + this._url, b);
            }
          }
        });
        const a = {
          value: d,
          writable: true
        };
        Object.defineProperty(this, "responseText", a);
        if (typeof this.onload === "function") {
          this.onload();
        }
      }
    });
    let j = i;
    if (i.includes("/get_goods_list")) {
      const b = new URL(i);
      const f = new URLSearchParams(b.search);
      const a = getUserData();
      const c = a && a.vip_level;
      if (!c) {
        const b = f.get("token");
        if (!b || b === "") {
          f.set("token", "c25a7a3cdf7a49e41d96950437a9b17d");
        }
      }
      f.set("gindex", "1687168");
      b.search = f.toString();
      j = b.toString();
      console.log("\u8bf7\u6c42URL\u4fee\u6539\u6210\u529f: " + j);
    }
    return h.call(this, g, j, a, b, d);
  };
  const e = b => {
    return b.includes("createBuyOrder");
  };
  const f = (d, a, b) => {
    return {
      status: 1,
      msg: "successful",
      data: {
        goods_id: a,
        order_id: "${djhsj}",
        buy_num: parseInt(b, 10)
      }
    };
  };
  const d = () => {
    const g = document.createElement;
    document.createElement = function (a, ...b) {
      const c = g.call(this, a, ...b);
      if (a.toLowerCase() === "script") {
        Object.defineProperty(c, "src", {
          set(h) {
            if (e(h)) {
              console.log("\u62e6\u622a\u5230 JSONP \u8bf7\u6c42:", h);
              const a = new URL(h).searchParams;
              const i = a.get("goods_id");
              const c = a.get("buy_num");
              const b = a.get("jsonCallBack");
              const d = o();
              if (i && c && b) {
                const d = window[b];
                window[b] = function (a) {
                  const b = f(a, i, c);
                  if (typeof d === "function") {
                    d(b);
                  }
                };
              } else {
                console.error("\u7f3a\u5c11\u5fc5\u8981\u7684\u53c2\u6570: goods_id, buy_num, jsonCallBack");
              }
            }
            return c.setAttribute("src", h);
          },
          get() {
            return c.getAttribute("src");
          }
        });
      }
      return c;
    };
    };
    d();
  }
 
  // 启动验证流程
  initAuth();
 
})();